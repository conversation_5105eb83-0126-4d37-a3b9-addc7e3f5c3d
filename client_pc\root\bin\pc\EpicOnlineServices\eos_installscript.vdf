"InstallScript"
{
	"Firewall"
	{
		"Warhammer 40000 Space Marine 2"		"%INSTALLDIR%\\client_pc\\root\\bin\\pc\\Warhammer 40000 Space Marine 2 - Retail.exe"
	}
	"Run Process"
	{
		"EAC"
		{
			"HasRunKey"		"HKEY_CURRENT_USER\\Software\\Valve\\Steam\\Apps\\2183900\\EAC"
			"process 1"		"%INSTALLDIR%\\EasyAntiCheat\\EasyAntiCheat_EOS_Setup.exe"
			"command 1"		"install 9f69c83bc0dc49a6b61cd7ecd991d6ec"
		}
		"Eos cleanup"
		{
			"HasRunKey"		"HKEY_CURRENT_USER\\Software\\Valve\\Steam\\Apps\\2183900\\EOS_cleanup"
			"Process 2"		"%INSTALLDIR%\\client_pc\\root\\bin\\pc\\EpicOnlineServices\\EpicOnlineServicesInstaller.exe"
			"Command 2"		"/uninstall productId=9f69c83bc0dc49a6b61cd7ecd991d6ec /quiet"
		}
		"CertsInstaller"
		{
			"HasRunKey"		"HKEY_CURRENT_USER\\Software\\Valve\\Steam\\Apps\\2183900\\CertsInstaller"
			"Process 3"		"%INSTALLDIR%\\client_pc\\root\\bin\\pc\\EpicOnlineServices\\CertsUpdater.exe"
			"Command 3"		"/verysilent"
		}
	}
	"Run Process On Uninstall"
	{
		"EAC_Uninstall"
		{
			"process 1"		"%INSTALLDIR%\\EasyAntiCheat\\EasyAntiCheat_EOS_Setup.exe"
			"command 1"		"uninstall 9f69c83bc0dc49a6b61cd7ecd991d6ec"
		}
		"Eos_uninstall"
		{
			"Process 2"		"%INSTALLDIR%\\client_pc\\root\\bin\\pc\\EpicOnlineServices\\EpicOnlineServicesInstaller.exe"
			"Command 2"		"/uninstall productId=9f69c83bc0dc49a6b61cd7ecd991d6ec /quiet"
		}
	}
}
"kvsignatures"
{
	"InstallScript"		"8351858de3b7d62c787d525b727dfe43621fc9c3ae114013f488398e083ca989ab56dd8ba77e8ffa1d19b23809b37e7f6ee0bb6d04747fa193eff8455473e792351c48bfa76d332f3d9e07865e8c4aa359d304ec05c6851e101152c08d8e23822a09d38cd0b863a026dc8430639435409e63c69a72341f3497c9c11b3dbe2ecb"
}
